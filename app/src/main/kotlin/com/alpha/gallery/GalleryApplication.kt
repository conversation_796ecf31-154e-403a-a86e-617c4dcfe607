package com.alpha.gallery

import android.app.Application
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import com.alpha.gallery.core.sync.observer.MediaStoreObserver
import com.alpha.gallery.core.sync.scheduler.SyncWorkScheduler
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

/**
 * Application class for Gallery app with Hilt and WorkManager setup
 */
@HiltAndroidApp
class GalleryApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var syncWorkScheduler: SyncWorkScheduler

    @Inject
    lateinit var mediaStoreObserver: MediaStoreObserver

    override fun onCreate() {
        super.onCreate()

        // Initialize sync components
        initializeSync()
    }

    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()
    }

    /**
     * Initialize sync components
     */
    private fun initializeSync() {
        // Schedule periodic sync
        syncWorkScheduler.schedulePeriodicSync()

        // Start MediaStore observer
        mediaStoreObserver.startObserving()
    }

    override fun onTerminate() {
        super.onTerminate()

        // Clean up observers
        mediaStoreObserver.cleanup()
    }
}
